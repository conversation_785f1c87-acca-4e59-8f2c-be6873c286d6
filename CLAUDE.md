# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This appears to be a new project directory. The project structure and development commands will be documented here as the codebase grows.

## Development Commands

*To be documented once build system and package management are set up*

## Architecture Notes

*To be documented as the project architecture is established*

## Important Notes

- This is a new/empty repository
- Development workflows and architecture will be documented as they are established
- Update this file when adding build tools, testing frameworks, or establishing coding patterns